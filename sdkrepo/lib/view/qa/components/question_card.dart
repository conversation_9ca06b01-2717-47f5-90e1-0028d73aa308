import 'package:digital_onboarding/digital_onboarding.dart';
import 'package:flutter/cupertino.dart';

import '../../../resources/exports/index.dart';

class QuestionCard extends GetView<QAController> {
  final DigitalOnBoardingQuestionModel questionModel;
  final int questionIndex;
  final bool isLast;

  const QuestionCard({
    super.key,
    required this.questionModel,
    required this.questionIndex,
    this.isLast = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: isLast ? 0 : 20),
      clipBehavior: Clip.antiAlias,
      decoration: ShapeDecoration(
        color: const Color(0xffE9EFF1),
        shape: ContinuousRectangleBorder(
          borderRadius: BorderRadius.circular(38),
        ),
      ),
      child: Container(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Question header
            Row(
              crossAxisAlignment: CrossAxisAlignment.baseline,
              textBaseline: TextBaseline.alphabetic,
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.7),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    'Q$questionIndex',
                    style: context.titleSmall.copyWith(
                      color: AppColors.black,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SpaceW12(),
                Expanded(
                  child: Text(
                    questionModel.question ?? '',
                    style: context.titleMedium.copyWith(
                      color: AppColors.black,
                      fontWeight: FontWeight.w600,
                      height: 1.4,
                    ),
                  ),
                ),
                GetBuilder<QAController>(
                  id: 'question_status_$questionIndex',
                  builder: (_) {
                    final isAnswered = controller.isQuestionAnswered(questionIndex);
                    return AnimatedSwitcher(
                      duration: const Duration(milliseconds: 300),
                      child: isAnswered
                          ? Container(
                              padding: const EdgeInsets.all(6),
                              decoration: const BoxDecoration(
                                color: AppColors.success,
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                Icons.check,
                                color: AppColors.white,
                                size: 16,
                              ),
                            )
                          : Container(
                              padding: const EdgeInsets.all(6),
                              decoration: BoxDecoration(
                                color: Colors.white.withValues(alpha: 0.3),
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                Icons.radio_button_unchecked,
                                color: AppColors.black,
                                size: 16,
                              ),
                            ),
                    );
                  },
                ),
              ],
            ),

            const SpaceH20(),

            // Question content based on type
            if (questionModel.questionType == "fill-in-the-blank") _buildFillInTheBlankContent(context) else _buildMultipleChoiceContent(context),
          ],
        ),
      ),
    );
  }

  Widget _buildFillInTheBlankContent(BuildContext context) {
    return GetBuilder<QAController>(
      id: 'fill_blank_$questionIndex',
      builder: (_) {
        return CustomTextFormField(
          key: Key("${questionModel.id}"),
          onChanged: (value) {
            controller.updateFillInTheBlankAnswer(
              questionModel.id ?? 0,
              value ?? '',
              questionIndex,
            );
          },
          controller: controller.getTextControllerForQuestion(questionIndex),
          isRequired: true,
          maxLines: 3,
          height: Sizes.HEIGHT_20,
          labelText: 'Your Answer',
          labelColor: AppColors.black,
          textColor: AppColors.black,
          errorColor: AppColors.error,
          enableBorderColor: Colors.grey.shade300,
          focusBorderColor: Colors.grey.shade300,
          textInputAction: TextInputAction.next,
          keyboardType: TextInputType.text,
          fillColor: AppColors.white,
        );
      },
    );
  }

  Widget _buildMultipleChoiceContent(BuildContext context) {
    return GetBuilder<QAController>(
      id: 'multi_choice_$questionIndex',
      builder: (_) {
        return Column(
          children: [
            DropdownButtonHideUnderline(
              child: DropdownButton2<DigitalOnBoardingAnswerOption>(
                isExpanded: true,
                hint: Text(
                  'Select an answer',
                  style: context.titleMedium.copyWith(
                    color: AppColors.hint,
                  ),
                ),
                items: questionModel.answers
                        ?.map(
                          (DigitalOnBoardingAnswerOption item) => DropdownMenuItem<DigitalOnBoardingAnswerOption>(
                            value: item,
                            child: Text(
                              item.answer ?? "",
                              style: context.titleMedium.copyWith(
                                color: AppColors.black,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        )
                        .toList() ??
                    [],
                value: controller.getSelectedAnswerForQuestion(questionIndex),
                onChanged: (v) {
                  controller.updateMultipleChoiceAnswer(v, questionIndex);
                },
                buttonStyleData: ButtonStyleData(
                  height: 50,
                  width: double.maxFinite,
                  padding: const EdgeInsets.only(left: 14, right: 14),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey.shade300),
                    color: AppColors.white,
                  ),
                  elevation: 0,
                ),
                iconStyleData: const IconStyleData(
                  icon: Icon(CupertinoIcons.chevron_down),
                  openMenuIcon: Icon(CupertinoIcons.chevron_up),
                  iconSize: 14,
                  iconEnabledColor: AppColors.black,
                  iconDisabledColor: Colors.grey,
                ),
                enableFeedback: true,
                dropdownStyleData: DropdownStyleData(
                  maxHeight: 200,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: AppColors.white,
                    border: Border.all(color: const Color(0xffE8E0EB)),
                  ),
                  offset: const Offset(0, -12),
                  elevation: 0,
                ),
                menuItemStyleData: const MenuItemStyleData(
                  height: 40,
                  padding: EdgeInsets.symmetric(horizontal: 14),
                ),
              ),
            ),

            // Show text field for "Other" option
            GetBuilder<QAController>(
              id: 'other_text_$questionIndex',
              builder: (_) {
                final selectedAnswer = controller.getSelectedAnswerForQuestion(questionIndex);
                final showOtherField = selectedAnswer?.answer?.toLowerCase() == 'other';

                return AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  height: showOtherField ? null : 0,
                  child: showOtherField
                      ? Column(
                          children: [
                            const SpaceH16(),
                            CustomTextFormField(
                              controller: controller.getOtherTextController(questionIndex),
                              onChanged: (value) {
                                controller.updateOtherText(questionIndex, value ?? '');
                              },
                              isRequired: true,
                              maxLines: 3,
                              height: Sizes.HEIGHT_20,
                              labelText: 'Please specify',
                              labelColor: AppColors.black,
                              textColor: AppColors.black,
                              cursorColor: AppColors.black,
                              errorColor: AppColors.error,
                              enableBorderColor: AppColors.enableBorder,
                              focusBorderColor: AppColors.primary,
                              textInputAction: TextInputAction.next,
                              keyboardType: TextInputType.text,
                              fillColor: AppColors.white,
                            ),
                          ],
                        )
                      : const SizedBox.shrink(),
                );
              },
            ),
          ],
        );
      },
    );
  }
}
